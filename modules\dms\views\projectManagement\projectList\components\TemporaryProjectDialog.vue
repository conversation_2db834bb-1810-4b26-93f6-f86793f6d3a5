<template>
    <div>
        <el-dialog title="新增临时项目" :visible.sync="dialogVisible" width="85%" top="5vh">
            <el-form ref="form" :model="form" :rules="rules" label-width="120px" class="form">
                <!-- 基本信息 -->
                <div class="title">基本信息</div>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="项目名称" prop="projectName">
                            <el-input v-model="form.projectName" placeholder="请输入项目名称" clearable />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="项目经理" prop="projectManager">
                            <PeopleSelector
                                v-model="form.projectManager"
                                placeholder="请选择项目经理"
                                :is-multipled="false"
                                size="medium"
                                clearable
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="开始时间" prop="startTime">
                            <el-date-picker
                                v-model="form.startTime"
                                type="date"
                                placeholder="请选择开始时间"
                                value-format="yyyy-MM-dd"
                                style="width: 100%"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="计划结束时间" prop="plannedEndTime">
                            <el-date-picker
                                v-model="form.plannedEndTime"
                                type="date"
                                placeholder="请选择计划结束时间"
                                value-format="yyyy-MM-dd"
                                style="width: 100%"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>

                <!-- 关联信息 -->
                <div class="title">关联信息</div>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="所属部门" prop="department">
                            <el-select v-model="form.department" placeholder="请选择" clearable style="width: 100%">
                                <el-option
                                    v-for="item in departmentOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="产品线" prop="productLine">
                            <el-select v-model="form.productLine" placeholder="请选择" clearable style="width: 100%">
                                <el-option
                                    v-for="item in productLineOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="产品经理" prop="productManager">
                            <el-input v-model="form.productManager" placeholder="请输入产品经理" clearable />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="涉及产品" prop="involvedProduct">
                            <el-select
                                v-model="form.involvedProduct"
                                placeholder="请选择"
                                clearable
                                style="width: 100%"
                            >
                                <el-option
                                    v-for="item in productOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <!-- 项目描述 -->
                <div class="title">项目描述</div>
                <el-form-item label="项目描述" prop="projectDescription">
                    <el-input
                        v-model="form.projectDescription"
                        type="textarea"
                        :rows="6"
                        placeholder="建议说明：任务来源、任务描述、任务分析、技术要求等"
                        resize="vertical"
                    />
                </el-form-item>
            </el-form>

            <div slot="footer" class="dialog-footer">
                <el-button @click="closeDialog">取消</el-button>
                <el-button type="primary" @click="handleSubmit">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import PeopleSelector from 'dms/components/PeopleSelector';

export default {
    name: 'TemporaryProjectDialog',
    components: { PeopleSelector },
    props: {
        visible: {
            type: Boolean,
            default: false
        }
    },

    data() {
        return {
            form: {
                projectName: '',
                projectManager: '',
                startTime: '',
                plannedEndTime: '',
                department: '',
                productLine: '',
                productManager: '',
                involvedProduct: '',
                projectDescription: ''
            },
            rules: {
                projectName: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
                projectManager: [{ required: true, message: '请选择项目经理', trigger: 'change' }],
                startTime: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
                department: [{ required: true, message: '请选择所属部门', trigger: 'change' }],
                productLine: [{ required: true, message: '请选择产品线', trigger: 'change' }]
            },
            // 部门选项
            departmentOptions: [],
            // 产品线选项
            productLineOptions: [],
            // 产品选项
            productOptions: []
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        }
    },
    watch: {
        dialogVisible(newVal) {
            if (newVal) {
                this.initData();
            }
        }
    },
    methods: {
        /**
         * 初始化数据
         */
        async initData() {
            this.resetForm();
            await this.getDepartmentOptions();
            await this.getProductLineOptions();
            await this.getProductOptions();
        },
        /**
         * 重置表单
         */
        resetForm() {
            this.form = {
                projectName: '',
                projectManager: '',
                startTime: '',
                plannedEndTime: '',
                department: '',
                productLine: '',
                productManager: '',
                involvedProduct: '',
                projectDescription: ''
            };
            this.$nextTick(() => {
                this.$refs.form && this.$refs.form.clearValidate();
            });
        },
        /**
         * 获取部门选项
         */
        async getDepartmentOptions() {
            try {
                const res = await this.$service.dms.common.getOrgList({
                    orgCode: '0002'
                });
                if (res.code === '0000') {
                    this.departmentOptions = this.formatDepartmentOptions(res.data);
                }
            } catch (error) {
                console.error('获取部门列表失败:', error);
            }
        },
        /**
         * 格式化部门选项
         */
        formatDepartmentOptions(data) {
            const options = [];
            data.forEach((dept) => {
                if (dept.children && dept.children.length > 0) {
                    dept.children.forEach((subDept) => {
                        options.push({
                            label: subDept.orgName,
                            value: subDept.orgCode
                        });
                    });
                }
            });
            return options;
        },
        /**
         * 获取产品线选项
         */
        async getProductLineOptions() {
            try {
                const res = await this.$service.dms.common.getProductLineList();
                if (res.code === '0000') {
                    this.productLineOptions = res.data.map((item) => ({
                        label: item.productLineName,
                        value: item.productLineId
                    }));
                }
            } catch (error) {
                console.error('获取产品线列表失败:', error);
            }
        },
        /**
         * 获取产品选项
         */
        async getProductOptions() {
            try {
                const res = await this.$service.dms.common.getProductList();
                if (res.code === '0000') {
                    this.productOptions = res.data.map((item) => ({
                        label: item.productName,
                        value: item.productId
                    }));
                }
            } catch (error) {
                console.error('获取产品列表失败:', error);
            }
        },
        /**
         * 提交表单
         */
        handleSubmit() {
            this.$refs.form.validate(async (valid) => {
                if (valid) {
                    try {
                        const res = await this.$service.dms.project.createTemporaryProject({
                            projectName: this.form.projectName,
                            projectManager: this.form.projectManager,
                            startTime: this.form.startTime,
                            plannedEndTime: this.form.plannedEndTime,
                            departmentCode: this.form.department,
                            productLine: this.form.productLine,
                            productManager: this.form.productManager,
                            involvedProduct: this.form.involvedProduct,
                            projectDescription: this.form.projectDescription
                        });
                        if (res.code === '0000') {
                            this.$message.success('创建临时项目成功');
                            this.closeDialog();
                            this.$emit('success');
                        } else {
                            this.$message.error(res.message || '创建临时项目失败');
                        }
                    } catch (error) {
                        console.error('创建临时项目失败:', error);
                        this.$message.error('创建临时项目失败');
                    }
                }
            });
        },
        /**
         * 关闭弹窗
         */
        closeDialog() {
            this.dialogVisible = false;
            this.$emit('update:visible', false);
        }
    }
};
</script>

<style lang="scss" scoped>
@import 'dms/views/demandManagement/common/common.scss';

.title {
    @include section-title;
    margin-bottom: 16px;
    margin-top: 32px;

    &:first-child {
        margin-top: 0;
    }
}

.flex {
    display: flex;
}

.info-card {
    margin-bottom: 16px;

    .card-header {
        .card-title {
            font-weight: bold;
            font-size: 14px;
            color: #303133;
        }
    }

    .text-content {
        white-space: pre-wrap;
        word-break: break-word;
        line-height: 1.6;
        min-height: 60px;
        color: #303133;
    }
}

::v-deep .el-card__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebeef5;
    background-color: #fafafa;
}

::v-deep .el-card__body {
    padding: 20px;
}

::v-deep .el-dialog__body {
    padding: 24px;
    max-height: 70vh;
    overflow-y: auto;
}

::v-deep .el-descriptions {
    margin-bottom: 20px;

    .el-descriptions__label {
        font-weight: 600;
        color: #606266;
        background-color: #fafbfc;
        width: 120px;
    }

    .el-descriptions__content {
        color: #303133;
        word-break: break-word;
    }
}

::v-deep .form .el-form-item__label {
    font-weight: bold;
}

.dialog-footer {
    text-align: right;
}

::v-deep .el-select {
    width: 100%;
}

::v-deep .el-textarea__inner {
    resize: vertical;
}

// 表格样式
::v-deep .el-table {
    .el-table__header-wrapper {
        th {
            background-color: #fafbfc;
            color: #606266;
            font-weight: 600;
        }
    }

    .el-table__body-wrapper {
        td {
            color: #303133;
        }
    }

    .el-table__border {
        border-color: #ebeef5;
    }

    th,
    td {
        border-color: #ebeef5;
    }
}
.el-descriptions {
    margin: 0px;
    ::v-deep .el-descriptions-item__label {
        width: 140px;
        font-size: 13px;
        font-weight: 600;
    }
    ::v-deep .el-descriptions-item__content {
        font-size: 14px;
    }
}
</style>
