<template>
    <div class="view-box">
        <!-- 按钮 -->
        <div class="sprint-btn">
            <el-button @click="cancel" type="danger" v-if="type === '审核'">拒绝</el-button>
            <el-button @click="cancel" type="primary" v-if="type === '审核'">批准</el-button>
            <el-button @click="cancel">返回</el-button>
        </div>
        <div>
            <!-- 产品结构 -->
            <formula-title :title="basicTitle"></formula-title>
            <!-- 文档代码仓库 -->
            <formula-title :title="woedTitle"></formula-title>
            <word-table :tableData="tableData"></word-table>
            <!-- 审批意见 -->
            <formula-title :title="checkTitle" v-if="type === '审核'"></formula-title>
            <el-form :model="addForm" ref="dataForm" label-width="80px" v-if="type === '审核'">
                <el-form-item label="审批意见" prop="approval">
                    <el-input
                        type="textarea"
                        v-model="addForm.approval"
                        maxlength="500"
                        :rows="4"
                        placeholder="请填写您的审批意见"
                    ></el-input>
                </el-form-item>
            </el-form>
        </div>
    </div>
</template>

<script>
import formulaTitle from '../components/formulaTitle.vue';
import wordTable from '../components/wordTable.vue';

export default {
    components: { formulaTitle, wordTable },
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            // 标题
            basicTitle: '产品结构',
            woedTitle: '文档&代码仓库',
            checkTitle: '审批意见',
            type: '',
            addForm: {
                approval: ''
            },
            tableData: [
                {
                    index: 1,
                    product: 'XXXXXXXXXXX',
                    subProduct: '-',
                    module: '-',
                    subModule: '-',
                    warehouseName: 'XXXXXXX',
                    operationType: '移除',
                    operationTime: 'XXXX-XX xxxxxx',
                    createTime: 'XXXX-XX xxxxxx',
                    warehouseStatus: 'XXX'
                },
                {
                    index: 2,
                    product: 'XXXXXXXXXXX',
                    subProduct: '-',
                    module: '-',
                    subModule: '-',
                    warehouseName: 'XXXXXXXXXXX',
                    operationType: '关联',
                    operationTime: 'XXXX-XX xxxxxx',
                    createTime: 'XXXX-XX xxxxxx',
                    warehouseStatus: 'XXX'
                },
                {
                    index: 3,
                    product: '',
                    subProduct: 'XXXXXXX',
                    module: '-',
                    subModule: '-',
                    warehouseName: 'XXXXXXX',
                    operationType: '关联',
                    operationTime: 'XXXX-XX xxxxxx',
                    createTime: 'XXXX-XX xxxxxx',
                    warehouseStatus: 'XXX'
                },
                {
                    index: 4,
                    product: '',
                    subProduct: 'XXXXXXXXXXX',
                    module: '-',
                    subModule: '-',
                    warehouseName: 'XXXXXXX',
                    operationType: '关联',
                    operationTime: 'XXXX-XX xxxxxx',
                    createTime: 'XXXX-XX xxxxxx',
                    warehouseStatus: 'XXX'
                }
            ]
        };
    },
    mounted() {
        this.type = this.$route.query.type;
    },
    methods: {
        cancel() {
            this.$router.back();
        }
    }
};
</script>

<style lang="scss" scoped></style>
