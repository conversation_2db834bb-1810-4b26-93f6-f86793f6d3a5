<template>
    <div>
        <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="85%" top="5vh">
            <el-form ref="form" :model="form" :rules="rules" label-width="130px" class="form">
                <!-- 基本信息 -->
                <div class="title">基本信息</div>
                <el-row :gutter="20">
                    <el-col :span="8">
                        <el-form-item label="项目名称" prop="projectName">
                            <el-input v-model="form.projectName" placeholder="请输入项目名称" clearable />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="项目经理" prop="projectManager">
                            <PeopleSelector
                                v-model="form.projectManager"
                                placeholder="请选择项目经理"
                                :is-multipled="false"
                                clearable
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="PPQA" prop="ppqa">
                            <PeopleSelector
                                v-model="form.ppqa"
                                placeholder="请选择PPQA"
                                :is-multipled="false"
                                clearable
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="8">
                        <el-form-item label="开始时间" prop="startTime">
                            <el-date-picker
                                v-model="form.startTime"
                                type="date"
                                placeholder="请选择开始时间"
                                value-format="yyyy-MM-dd"
                                style="width: 100%"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="计划结束时间" prop="plannedEndTime">
                            <el-date-picker
                                v-model="form.plannedEndTime"
                                type="date"
                                placeholder="请选择计划结束时间"
                                value-format="yyyy-MM-dd"
                                style="width: 100%"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="实际结束时间" prop="actualEndTime">
                            <el-date-picker
                                v-model="form.actualEndTime"
                                type="date"
                                placeholder="请选择实际结束时间"
                                value-format="yyyy-MM-dd"
                                style="width: 100%"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>

                <!-- 关联信息 -->
                <div class="title">关联信息</div>
                <el-row :gutter="20">
                    <el-col :span="8">
                        <el-form-item label="项目归属" prop="projectOwnership">
                            <el-select
                                v-model="form.projectOwnership"
                                placeholder="请选择"
                                clearable
                                style="width: 100%"
                            >
                                <el-option
                                    v-for="item in projectOwnershipOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="所属部门" prop="department">
                            <el-select v-model="form.department" placeholder="请选择" clearable style="width: 100%">
                                <el-option
                                    v-for="item in departmentOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="产品线" prop="productLine">
                            <el-select v-model="form.productLine" placeholder="请选择" clearable style="width: 100%">
                                <el-option
                                    v-for="item in productLineOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="产品经理" prop="productManager">
                            <el-input v-model="form.productManager" placeholder="请输入产品经理" clearable />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="涉及产品" prop="involvedProduct">
                            <el-select
                                v-model="form.involvedProduct"
                                placeholder="请选择"
                                clearable
                                style="width: 100%"
                            >
                                <el-option
                                    v-for="item in productOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <!-- 其他信息 -->
                <div class="title">其他信息</div>
                <el-row :gutter="20">
                    <el-col :span="8">
                        <el-form-item label="项目来源" prop="projectSource">
                            <el-select v-model="form.projectSource" placeholder="请选择" clearable style="width: 100%">
                                <el-option
                                    v-for="item in projectSourceOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="项目类型" prop="projectType">
                            <el-select v-model="form.projectType" placeholder="请选择" clearable style="width: 100%">
                                <el-option
                                    v-for="item in projectTypeOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="项目立项类型" prop="projectApprovalType">
                            <el-select
                                v-model="form.projectApprovalType"
                                placeholder="请选择"
                                clearable
                                style="width: 100%"
                            >
                                <el-option
                                    v-for="item in projectApprovalTypeOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="8">
                        <el-form-item label="项目规模" prop="projectScale">
                            <el-select v-model="form.projectScale" placeholder="请选择" clearable style="width: 100%">
                                <el-option
                                    v-for="item in projectScaleOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="项目难度等级" prop="projectDifficultyLevel">
                            <el-select
                                v-model="form.projectDifficultyLevel"
                                placeholder="请选择"
                                clearable
                                style="width: 100%"
                            >
                                <el-option
                                    v-for="item in projectDifficultyLevelOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="公司立项等级" prop="companyApprovalLevel">
                            <el-select
                                v-model="form.companyApprovalLevel"
                                placeholder="请选择"
                                clearable
                                style="width: 100%"
                            >
                                <el-option
                                    v-for="item in companyApprovalLevelOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="项目级别" prop="projectLevel">
                            <el-select v-model="form.projectLevel" placeholder="请选择" clearable style="width: 100%">
                                <el-option
                                    v-for="item in projectLevelOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <!-- 公司立项项目信息 -->
                <div class="title">公司立项项目信息</div>
                <el-row :gutter="20">
                    <el-col :span="8">
                        <el-form-item label="公司立项项目名称" prop="companyProjectName">
                            <el-input
                                v-model="form.companyProjectName"
                                placeholder="请输入公司立项项目名称"
                                clearable
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="立项决议函名称" prop="approvalDocumentName">
                            <el-input
                                v-model="form.approvalDocumentName"
                                placeholder="请输入立项决议函名称"
                                clearable
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="立项时间" prop="approvalTime">
                            <el-date-picker
                                v-model="form.approvalTime"
                                type="date"
                                placeholder="请选择立项时间"
                                value-format="yyyy-MM-dd"
                                style="width: 100%"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>

                <!-- 硬件项目团队信息 -->
                <div class="title">硬件项目团队信息</div>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="硬件产品经理" prop="hardwareProductManager">
                            <el-input
                                v-model="form.hardwareProductManager"
                                placeholder="请输入硬件产品经理"
                                clearable
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="硬件项目经理" prop="hardwareProjectManager">
                            <el-input
                                v-model="form.hardwareProjectManager"
                                placeholder="请输入硬件项目经理"
                                clearable
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="24">
                        <el-form-item label="硬件团队代表" prop="hardwareTeamRepresentative">
                            <el-input
                                v-model="form.hardwareTeamRepresentative"
                                placeholder="请输入硬件团队代表"
                                clearable
                            />
                        </el-form-item>
                    </el-col>
                </el-row>

                <!-- 项目描述 -->
                <div class="title">项目描述</div>
                <el-form-item label="任务来源" prop="taskSource">
                    <el-input
                        v-model="form.taskSource"
                        type="textarea"
                        :rows="6"
                        placeholder="请输入任务来源"
                        resize="vertical"
                    />
                </el-form-item>
                <el-form-item label="任务描述" prop="taskSource">
                    <el-input
                        v-model="form.taskSource"
                        type="textarea"
                        :rows="6"
                        placeholder="请输入任务描述"
                        resize="vertical"
                    />
                </el-form-item>
                <el-form-item label="任务分析" prop="taskSource">
                    <el-input
                        v-model="form.taskSource"
                        type="textarea"
                        :rows="6"
                        placeholder="请输入任务分析"
                        resize="vertical"
                    />
                </el-form-item>
                <el-form-item label="总体要求" prop="taskSource">
                    <el-input
                        v-model="form.taskSource"
                        type="textarea"
                        :rows="6"
                        placeholder="请输入总体要求"
                        resize="vertical"
                    />
                </el-form-item>
                <el-form-item label="技术要求" prop="taskSource">
                    <el-input
                        v-model="form.taskSource"
                        type="textarea"
                        :rows="6"
                        placeholder="请输入技术要求"
                        resize="vertical"
                    />
                </el-form-item>
            </el-form>

            <div slot="footer" class="dialog-footer">
                <el-button @click="closeDialog">取消</el-button>
                <el-button type="primary" @click="handleSubmit">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import PeopleSelector from 'dms/components/PeopleSelector';

export default {
    name: 'FormalProjectDialog',
    components: { PeopleSelector },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        // 新增/编辑
        type: {
            type: String,
            default: 'add'
        },
        projectId: {
            type: String,
            default: ''
        }
    },

    data() {
        return {
            form: {
                // 基本信息
                projectName: '',
                projectManager: '',
                ppqa: '',
                startTime: '',
                plannedEndTime: '',
                actualEndTime: '',
                // 关联信息
                projectOwnership: '',
                department: '',
                productLine: '',
                productManager: '',
                involvedProduct: '',
                // 其他信息
                projectSource: '',
                projectType: '',
                projectApprovalType: '',
                projectScale: '',
                projectDifficultyLevel: '',
                companyApprovalLevel: '',
                projectLevel: '',
                // 公司立项项目信息
                companyProjectName: '',
                approvalDocumentName: '',
                approvalTime: '',
                // 硬件项目团队信息
                hardwareProductManager: '',
                hardwareProjectManager: '',
                hardwareTeamRepresentative: '',
                // 项目描述
                taskSource: ''
            },
            rules: {
                projectName: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
                projectManager: [{ required: true, message: '请选择项目经理', trigger: 'change' }],
                startTime: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
                department: [{ required: true, message: '请选择所属部门', trigger: 'change' }],
                productLine: [{ required: true, message: '请选择产品线', trigger: 'change' }],
                productManager: [{ required: true, message: '请选择产品经理', trigger: 'change' }],
                relatedProduct: [{ required: true, message: '请选择涉及产品', trigger: 'change' }]
            },
            // 选项数据
            departmentOptions: [],
            productLineOptions: [],
            productOptions: [],
            projectOwnershipOptions: [
                { label: '内部项目', value: 'internal' },
                { label: '外部项目', value: 'external' },
                { label: '合作项目', value: 'cooperation' }
            ],
            projectSourceOptions: [
                { label: '客户需求', value: 'customer' },
                { label: '市场调研', value: 'market' },
                { label: '技术创新', value: 'innovation' },
                { label: '战略规划', value: 'strategy' }
            ],
            projectTypeOptions: [
                { label: '产品开发', value: 'product' },
                { label: '技术研发', value: 'technology' },
                { label: '系统集成', value: 'integration' },
                { label: '服务项目', value: 'service' }
            ],
            projectApprovalTypeOptions: [
                { label: '公司立项', value: 'company' },
                { label: '部门立项', value: 'department' },
                { label: '临时立项', value: 'temporary' }
            ],
            projectScaleOptions: [
                { label: '小型', value: 'small' },
                { label: '中型', value: 'medium' },
                { label: '大型', value: 'large' },
                { label: '超大型', value: 'xlarge' }
            ],
            projectDifficultyLevelOptions: [
                { label: '低', value: 'low' },
                { label: '中', value: 'medium' },
                { label: '高', value: 'high' },
                { label: '极高', value: 'extreme' }
            ],
            companyApprovalLevelOptions: [
                { label: 'A级', value: 'A' },
                { label: 'B级', value: 'B' },
                { label: 'C级', value: 'C' },
                { label: 'D级', value: 'D' }
            ],
            projectLevelOptions: [
                { label: '一级项目', value: 'level1' },
                { label: '二级项目', value: 'level2' },
                { label: '三级项目', value: 'level3' }
            ]
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        },
        dialogTitle() {
            return this.type === 'add' ? '新增正式项目' : '编辑正式项目';
        }
    },
    watch: {
        dialogVisible(newVal) {
            if (newVal) {
                this.initData();
            }
        }
    },
    methods: {
        /**
         * 初始化数据
         */
        async initData() {
            if (this.type === 'edit') {
                this.getDetail();
            } else {
                this.resetForm();
            }
            // await this.getDepartmentOptions();
            // await this.getProductLineOptions();
            // await this.getProductOptions();
        },
        async getDetail() {
            const api = this.$service.dms.project.getProjectDetail;
            const params = { projectId: this.projectId };
            try {
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }

                const { project, extend } = res.data;

                // 基本信息
                this.form.projectName = project.projectName || '';
                this.form.projectManager = project.projectManager || '';
                this.form.productManagerName = project.productManagerName || '';
                this.form.ppqa = project.pqa || '';
                this.form.pqaAccount = project.pqaAccount || '';
                this.form.startTime = project.startDate || '';
                this.form.plannedEndTime = project.planEndDate || '';
                this.form.actualEndTime = project.endDate || '';
                // 注意这里是项目经理名字
                this.form.projectManager = project.projectManager;
                // 这里是项目经理域账号
                this.form.pmAccount = project.pmAccount || '';

                // 关联信息
                this.form.projectOwnership = project.projectAttribution || '';
                this.form.department = project.org || '';
                this.form.productLine = project.productLine || '';
                // 接口中没有对应字段
                this.form.involvedProduct = '';

                // 其他信息
                this.form.projectSource = project.projectOrigin || '';
                this.form.projectType = project.projectType || '';
                this.form.projectApprovalType = project.projectApproval || '';
                this.form.projectScale = project.projectScale || '';
                // 接口中没有对应字段
                this.form.projectDifficultyLevel = '';
                this.form.companyApprovalLevel = project.approvalGrade || '';
                this.form.projectLevel = project.projectLevel || '';

                // 公司立项项目信息
                this.form.companyProjectName = project.companyProjectName || '';
                this.form.approvalDocumentName = project.marketDecisionName || '';
                this.form.approvalTime = project.marketDecisionAcceptDate || '';

                // 硬件项目团队信息
                this.form.hardwareProductManager = extend.hardwareProductManager || '';
                this.form.hardwareProjectManager = extend.hardwareProjectManager || '';
                this.form.hardwareTeamRepresentative = extend.hardwareGroup || '';

                // 项目描述
                this.form.taskSource = extend.taskOrigin || '';
            } catch (error) {
                console.error('Error:', error);
                this.$message.error('获取项目详情失败');
            }
        },
        /**
         * 重置表单
         */
        resetForm() {
            this.form = {
                // 基本信息
                projectName: '',
                projectManager: '',
                ppqa: '',
                startTime: '',
                plannedEndTime: '',
                actualEndTime: '',
                // 关联信息
                projectOwnership: '',
                department: '',
                productLine: '',
                productManager: '',
                involvedProduct: '',
                // 其他信息
                projectSource: '',
                projectType: '',
                projectApprovalType: '',
                projectScale: '',
                projectDifficultyLevel: '',
                companyApprovalLevel: '',
                projectLevel: '',
                // 公司立项项目信息
                companyProjectName: '',
                approvalDocumentName: '',
                approvalTime: '',
                // 硬件项目团队信息
                hardwareProductManager: '',
                hardwareProjectManager: '',
                hardwareTeamRepresentative: '',
                // 项目描述
                taskSource: ''
            };
            this.$nextTick(() => {
                this.$refs.form && this.$refs.form.clearValidate();
            });
        },
        /**
         * 获取部门选项
         */
        async getDepartmentOptions() {
            try {
                const res = await this.$service.dms.common.getOrgList({
                    orgCode: '0002'
                });
                if (res.code === '0000') {
                    this.departmentOptions = this.formatDepartmentOptions(res.data);
                }
            } catch (error) {
                console.error('获取部门列表失败:', error);
            }
        },
        /**
         * 格式化部门选项
         * @param {Array} data 部门数据
         * @returns {Array} 格式化后的部门选项
         */
        formatDepartmentOptions(data) {
            const options = [];
            data.forEach((dept) => {
                if (dept.children && dept.children.length > 0) {
                    dept.children.forEach((subDept) => {
                        options.push({
                            label: subDept.orgName,
                            value: subDept.orgCode
                        });
                    });
                }
            });
            return options;
        },
        /**
         * 获取产品线选项
         */
        async getProductLineOptions() {
            try {
                const res = await this.$service.dms.common.getProductLineList();
                if (res.code === '0000') {
                    this.productLineOptions = res.data.map((item) => ({
                        label: item.productLineName,
                        value: item.productLineId
                    }));
                }
            } catch (error) {
                console.error('获取产品线列表失败:', error);
            }
        },
        /**
         * 获取产品选项
         */
        async getProductOptions() {
            try {
                const res = await this.$service.dms.common.getProductList();
                if (res.code === '0000') {
                    this.productOptions = res.data.map((item) => ({
                        label: item.productName,
                        value: item.productId
                    }));
                }
            } catch (error) {
                console.error('获取产品列表失败:', error);
            }
        },
        /**
         * 提交表单
         */
        handleSubmit() {
            this.$refs.form.validate(async (valid) => {
                if (valid) {
                    try {
                        const res = await this.$service.dms.project.createFormalProject({
                            // 基本信息
                            projectName: this.form.projectName,
                            projectManager: this.form.projectManager,
                            ppqa: this.form.ppqa,
                            startTime: this.form.startTime,
                            plannedEndTime: this.form.plannedEndTime,
                            actualEndTime: this.form.actualEndTime,
                            // 关联信息
                            projectOwnership: this.form.projectOwnership,
                            departmentCode: this.form.department,
                            productLine: this.form.productLine,
                            productManager: this.form.productManager,
                            involvedProduct: this.form.involvedProduct,
                            // 其他信息
                            projectSource: this.form.projectSource,
                            projectType: this.form.projectType,
                            projectApprovalType: this.form.projectApprovalType,
                            projectScale: this.form.projectScale,
                            projectDifficultyLevel: this.form.projectDifficultyLevel,
                            companyApprovalLevel: this.form.companyApprovalLevel,
                            projectLevel: this.form.projectLevel,
                            // 公司立项项目信息
                            companyProjectName: this.form.companyProjectName,
                            approvalDocumentName: this.form.approvalDocumentName,
                            approvalTime: this.form.approvalTime,
                            // 硬件项目团队信息
                            hardwareProductManager: this.form.hardwareProductManager,
                            hardwareProjectManager: this.form.hardwareProjectManager,
                            hardwareTeamRepresentative: this.form.hardwareTeamRepresentative,
                            // 项目描述
                            taskSource: this.form.taskSource
                        });
                        if (res.code === '0000') {
                            this.$message.success('创建正式项目成功');
                            this.closeDialog();
                            this.$emit('success');
                        } else {
                            this.$message.error(res.message || '创建正式项目失败');
                        }
                    } catch (error) {
                        console.error('创建正式项目失败:', error);
                        this.$message.error('创建正式项目失败');
                    }
                }
            });
        },
        /**
         * 关闭弹窗
         */
        closeDialog() {
            this.dialogVisible = false;
            this.$emit('update:visible', false);
        }
    }
};
</script>

<style lang="scss" scoped>
@import 'dms/views/demandManagement/common/common.scss';

.title {
    @include section-title;
    margin-bottom: 16px;
    margin-top: 32px;

    &:first-child {
        margin-top: 0;
    }
}

.flex {
    display: flex;
}

.info-card {
    margin-bottom: 16px;

    .card-header {
        .card-title {
            font-weight: bold;
            font-size: 14px;
            color: #303133;
        }
    }

    .text-content {
        white-space: pre-wrap;
        word-break: break-word;
        line-height: 1.6;
        min-height: 60px;
        color: #303133;
    }
}

::v-deep .el-card__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebeef5;
    background-color: #fafafa;
}

::v-deep .el-card__body {
    padding: 20px;
}

::v-deep .el-dialog__body {
    padding: 24px;
    max-height: 70vh;
    overflow-y: auto;
}

::v-deep .el-descriptions {
    margin-bottom: 20px;

    .el-descriptions__label {
        font-weight: 600;
        color: #606266;
        background-color: #fafbfc;
        width: 120px;
    }

    .el-descriptions__content {
        color: #303133;
        word-break: break-word;
    }
}

::v-deep .form .el-form-item__label {
    font-weight: bold;
}

.dialog-footer {
    text-align: right;
}

::v-deep .el-select {
    width: 100%;
}

::v-deep .el-textarea__inner {
    resize: vertical;
}

// 表格样式
::v-deep .el-table {
    .el-table__header-wrapper {
        th {
            background-color: #fafbfc;
            color: #606266;
            font-weight: 600;
        }
    }

    .el-table__body-wrapper {
        td {
            color: #303133;
        }
    }

    .el-table__border {
        border-color: #ebeef5;
    }

    th,
    td {
        border-color: #ebeef5;
    }
}
.el-descriptions {
    margin: 0px;
    ::v-deep .el-descriptions-item__label {
        width: 140px;
        font-size: 13px;
        font-weight: 600;
    }
    ::v-deep .el-descriptions-item__content {
        font-size: 14px;
    }
}
</style>
