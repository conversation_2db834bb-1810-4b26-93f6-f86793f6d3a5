import CommonItems from 'snbcCommon/common/form-items.js';

const { select, input, dateRange } = CommonItems;

// 部门
const department = {
    ...select,
    name: '部门',
    modelKey: 'orgCode'
};

// 产品线
const productLine = {
    ...select,
    name: '产品线',
    modelKey: 'productLine'
};

// 项目名称
const projectName = {
    ...input,
    name: '项目名称',
    modelKey: 'projectName'
};

// 项目经理
const projectManager = {
    ...input,
    name: '项目经理',
    modelKey: 'pmName'
};

// 产品经理
const productManager = {
    ...input,
    name: '产品经理',
    modelKey: 'poName'
};

// 项目ID
const projectId = {
    ...input,
    name: '项目ID',
    modelKey: 'projectId'
};

// 涉及产品
const involvedProducts = {
    ...input,
    name: '涉及产品',
    modelKey: 'productName'
};

// 进度状态
const progressStatus = {
    ...select,
    name: '进度状态',
    modelKey: 'processStatus'
};

// 开始时间
const startTime = {
    ...dateRange,
    name: '开始时间',
    modelKey: 'startDate',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM-dd'
    }
};

// 计划结束时间
const plannedEndTime = {
    ...dateRange,
    name: '计划结束时间',
    modelKey: 'planEndDate',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM-dd'
    }
};

// 实际结束时间
const actualEndTime = {
    ...dateRange,
    name: '实际结束时间',
    modelKey: 'endDate',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM-dd'
    }
};

// 公司立项项目
const companyProjectTime = {
    ...input,
    name: '公司立项项目',
    modelKey: 'companyProjectName'
};

// 查询区域配置项
export const queryConfig = {
    elFormAttrs: {
        'size': 'small',
        'inline': true,
        'label-width': '120px'
    },
    items: [
        department,
        productLine,
        projectName,
        projectManager,
        productManager,
        projectId,
        involvedProducts,
        progressStatus,
        startTime,
        plannedEndTime,
        actualEndTime,
        companyProjectTime
    ]
};

// 查询条件参数
export const queryParams = {
    orgCode: '',
    productLine: '',
    projectName: '',
    pmName: '',
    poName: '',
    projectId: '',
    productName: '',
    processStatus: '',
    startDate: [],
    planEndDate: [],
    endDate: [],
    companyProjectName: ''
};

// 导航栏配置
export const navItems = [
    { field: '', name: '所有', queryField: 'projectStatus' },
    { field: '待启动', name: '待启动', queryField: 'projectStatus' },
    { field: '进行中', name: '进行中', queryField: 'projectStatus' },
    { field: '暂停', name: '暂停', queryField: 'projectStatus' },
    { field: '结项', name: '结项', queryField: 'projectStatus' },
    { field: '终止', name: '终止', queryField: 'projectStatus' }
];

// 产品列表表格列配置
export const productColumns = [
    {
        prop: 'index',
        label: '序号',
        width: 80,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'projectId',
        label: 'ID',
        width: 160,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'orgName',
        label: '部门',
        width: 160,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productLine',
        label: '产品线',
        width: 160,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'poName',
        label: '产品经理',
        width: 200,
        showOverflowTooltip: false,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'projectName',
        label: '项目名称',
        width: 160,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'pmName',
        label: '项目经理',
        width: 160,
        sortable: 'custom',
        attrs: {
            align: 'center'
        }
    },
    {
        prop: 'productName',
        label: '涉及产品',
        width: 160,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'projectStatus',
        label: '项目状态',
        width: 160,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'processStatus',
        label: '进度状态',
        width: 160,
        sortable: 'custom',
        attrs: {
            align: 'center'
        }
    },
    {
        prop: 'startDate',
        label: '项目开始时间',
        width: 160,
        sortable: 'custom',
        attrs: {
            align: 'center'
        }
    },
    {
        prop: 'planEndDate',
        label: '计划结束时间',
        width: 160,
        sortable: 'custom',
        attrs: {
            align: 'center'
        }
    },
    {
        prop: 'projectLevel',
        label: '实际结束时间',
        width: 160,
        sortable: 'custom',
        attrs: {
            align: 'center'
        }
    },
    {
        prop: 'companyProjectName',
        label: '公司立项项目',
        width: 160,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    }
];
