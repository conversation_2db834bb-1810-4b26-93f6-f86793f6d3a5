import CommonItems from 'snbcCommon/common/form-items.js';

const { select, input, dateRange } = CommonItems;

// 部门
const department = {
    ...select,
    name: '部门',
    modelKey: 'department'
};

// 产品线
const productLine = {
    ...select,
    name: '产品线',
    modelKey: 'productLine'
};

// 项目名称
const projectName = {
    ...input,
    name: '项目名称',
    modelKey: 'projectName'
};

// 产品经理
const productManager = {
    ...input,
    name: '产品经理',
    modelKey: 'productManager'
};

// 项目ID
const projectId = {
    ...input,
    name: '项目ID',
    modelKey: 'projectId'
};

// 开始时间
const startTime = {
    ...dateRange,
    name: '开始时间',
    modelKey: 'startTime',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM-dd'
    }
};

// 计划结束时间
const plannedEndTime = {
    ...dateRange,
    name: '计划结束时间',
    modelKey: 'plannedEndTime',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM-dd'
    }
};

// PPQA
const ppqa = {
    ...input,
    name: 'PPQA',
    modelKey: 'ppqa'
};

// 查询区域配置项
export const queryConfig = {
    elFormAttrs: {
        'size': 'small',
        'inline': true,
        'label-width': '120px'
    },
    items: [department, productLine, projectName, productManager, projectId, startTime, plannedEndTime, ppqa]
};

// 查询条件参数
export const queryParams = {
    department: '',
    productLine: '',
    projectName: '',
    productManager: '',
    projectId: '',
    startTime: [],
    plannedEndTime: [],
    ppqa: ''
};

// 导航栏配置
export const navItems = [
    { field: '', name: '所有', queryField: 'progressStatus' },
    { field: 'closed', name: '待审核', queryField: 'progressStatus' },
    { field: 'closed', name: '已审核', queryField: 'progressStatus' }
];

// 产品列表表格列配置
export const productColumns = [
    {
        prop: 'index',
        label: '序号',
        width: 80,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'id',
        label: 'ID',
        width: 120,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'department',
        label: '部门',
        width: 120,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productLine',
        label: '产品线',
        width: 120,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productNumber',
        label: '产品经理',
        width: 140,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'projectName',
        label: '项目名称',
        width: 180,
        showOverflowTooltip: true,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'projectManager',
        label: '项目经理',
        width: 120,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'involvedProducts',
        label: '提交日期',
        width: 160,
        showOverflowTooltip: true,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'involvedProducts',
        label: '审批结果',
        width: 160,
        showOverflowTooltip: true,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'reviewTime',
        label: '审批日期',
        width: 140,
        sortable: 'custom',
        attrs: {
            align: 'center'
        }
    },
    {
        prop: 'involvedProducts',
        label: '项目开始时间',
        width: 160,
        showOverflowTooltip: true,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'involvedProducts',
        label: '计划结束时间',
        width: 160,
        showOverflowTooltip: true,
        columnManage: {
            sortableDisabled: true
        }
    }
];
