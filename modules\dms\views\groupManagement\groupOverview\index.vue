<template>
    <div class="group-overview">
        <div class="group-list">
            <div class="group-list-title">团队列表</div>
            <el-select v-model="group" placeholder="请选择团队" style="width: 300px" filterable>
                <el-option v-for="item in groupListOptions" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
            </el-select>
        </div>
        <el-tabs v-model="activeName" type="card">
            <el-tab-pane label="基本信息" name="baseInfo">
                <GroupBaseInfo></GroupBaseInfo>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script>
import GroupBaseInfo from './groupBaseInfo/index.vue';

export default {
    name: 'GroupOverview',
    components: {
        GroupBaseInfo
    },
    props: {},
    data() {
        return {
            activeName: 'baseInfo',
            group: '',
            groupListOptions: []
        };
    },
    computed: {},
    watch: {},
    created() {},
    mounted() {},
    methods: {}
};
</script>

<style lang="scss" scoped>
.group-overview {
    position: relative;
    padding: 10px 20px;
    ::v-deep .el-tabs__nav-wrap {
        display: flex;
        justify-content: center;
        .el-tabs__item {
            color: #4377ee;
        }
        .el-tabs__item.is-active {
            background-color: #4377ee;
            color: #fff;
        }
    }
    ::v-deep .el-tabs--card > .el-tabs__header {
        border-bottom: 1px solid #4377ee;
    }
}
.group-list {
    position: absolute;
    display: flex;
    align-items: center;
    .group-list-title {
        font-size: 16px;
        font-weight: bold;
        color: #303133;
        margin-right: 15px;
    }
}
</style>
