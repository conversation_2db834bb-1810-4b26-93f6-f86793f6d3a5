<template>
    <div class="view">
        <project-list
            v-model="activeNavTab"
            :nav-items="navItems"
            :query-params="queryParams"
            :query-config="queryConfig"
            :columns="columns"
            :data="productData"
            :load-data="loadProductData"
            :total="total"
            :page.sync="page"
            :limit.sync="limit"
            @search="handleSearch"
            @reset="handleReset"
            @nav-change="handleNavChange"
            @selection-change="handleSelectionChange"
            @pagination="handlePagination"
        >
            <!-- 操作列插槽 -->
            <template #actions="{ row }">
                <el-button type="text" size="small" @click="handleDetails(row)">详情</el-button>
                <el-button type="text" size="small" @click="handleChange(row)">变更</el-button>
                <el-button type="text" size="small" @click="handleStop(row)">暂停</el-button>
                <el-button type="text" size="small" @click="handleClose(row)">关闭</el-button>
            </template>
        </project-list>
    </div>
</template>

<script>
import ProjectList from 'dms/views/projectManagement/components/projectList/index.vue';
import { queryConfig, queryParams, navItems, productColumns } from './config.js';

export default {
    name: 'ProductList',
    components: {
        ProjectList
    },
    data() {
        return {
            // 当前激活的导航标签
            activeNavTab: 'all',
            // 导航栏配置
            navItems,
            // 查询参数
            queryParams: this.$tools.cloneDeep(queryParams),
            // 查询表单配置
            queryConfig,
            // 表格列配置
            columns: productColumns,
            // 产品数据
            productData: [{}, {}],
            // 分页配置
            total: 0,
            page: 1,
            limit: 10
        };
    },
    mounted() {
        this.loadProductData();
    },
    methods: {
        // 加载产品数据
        async loadProductData() {
            try {
                const params = {
                    page: this.page,
                    limit: this.limit,
                    ...this.queryParams
                };

                // 日期处理
                if (params.foundDate && params.foundDate.length > 0) {
                    params.startDateString = params.foundDate[0];
                    params.endDateString = params.foundDate[1];
                }
                if (params.closeDate && params.closeDate.length > 0) {
                    params.closeStartDateString = params.closeDate[0];
                    params.closeEndDateString = params.closeDate[1];
                }

                const response = await this.$service.dms.common.getProjectOrGroupList(params);

                if (response.code === '000000') {
                    this.productData = response.result?.list || [];
                    this.total = response.result?.total || 0;
                } else {
                    this.$message.error(response.message || '获取产品列表失败');
                }
            } catch (error) {
                console.error('加载产品数据失败:', error);
                this.$message.error('加载产品数据失败');
            }
        },

        // 处理搜索
        handleSearch() {
            this.page = 1;
            this.loadProductData();
        },

        // 处理重置
        handleReset() {
            this.page = 1;
            this.loadProductData();
        },

        // 处理导航切换
        handleNavChange() {
            this.page = 1;
            this.loadProductData();
        },

        // 处理选择变化
        handleSelectionChange(selection) {
            console.log('选中的产品:', selection);
        },

        // 处理分页变化
        handlePagination() {
            this.loadProductData();
        },
        // 查看产品详情
        handleView(row) {},

        // 删除产品
        async handleDelete(row) {
            await this.$confirm(`确定要删除产品"${row.productName}"吗？`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            });
        },

        /**
         * 新增
         */
        handleAdd() {
            this.$router.push({
                name: 'AddProduct'
            });
        }
    }
};
</script>

<style lang="scss" scoped></style>
