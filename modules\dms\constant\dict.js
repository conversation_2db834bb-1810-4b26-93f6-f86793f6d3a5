// 需求类型
const typeData = [
    { label: '业务需求', value: '业务需求' },
    { label: '技术优化', value: '技术优化' },
    { label: '产品优化', value: '产品优化' },
    { label: '部门建设', value: '部门建设' }
];

// 优先级
const priorityData = [
    { label: '高', value: '高' },
    { label: '中', value: '中' },
    { label: '低', value: '低' }
];

// 需求等级
const storyLevelData = [
    { label: '一级', value: '一级' },
    { label: '二级', value: '二级' },
    { label: '三级', value: '三级' }
];

// 需求来源
const demandSourceData = [
    { label: '客户需求', value: '客户需求' },
    { label: '软硬项目', value: '软硬项目' },
    { label: '软件项目', value: '软件项目' },
    { label: '内部需求', value: '内部需求' }
];

// 原始需求状态
const originalDemandStatus = [
    { label: '草稿', value: '草稿' },
    { label: '待审核', value: '待审核' },
    { label: '已拒绝', value: '已拒绝' },
    { label: '未开始', value: '未开始' },
    { label: '已计划', value: '已计划' },
    { label: '进行中', value: '进行中' },
    { label: '退回中', value: '退回中' },
    { label: '已完成', value: '已完成' },
    { label: '已关闭', value: '已关闭' }
];

// 用户/产品需求状态
const userAndProductDemandStatus = [
    { label: '未开始', value: '未开始' },
    { label: '已计划', value: '已计划' },
    { label: '进行中', value: '进行中' },
    { label: '已完成', value: '已完成' },
    { label: '已关闭', value: '已关闭' }
];

// 需求进度
const demandProgressData = [
    { label: '按时完成', value: '按时完成' },
    { label: '延期进行中', value: '延期进行中' },
    { label: '延期完成', value: '延期完成' }
];

// 部署/发布状态
const deployOrPublishStatusData = [
    { label: '已部署', value: '已部署' },
    { label: '已发布', value: '已发布' }
];

// 审核类型
const checkTypeData = [
    { label: '需求审核', value: '需求审核' },
    { label: '内容变更', value: '内容变更' },
    { label: '负责人变更', value: '负责人变更' }
];

// 关闭原因
const closeReason = [
    { label: '已完成', value: '已完成' },
    { label: '已取消', value: '已取消' },
    { label: '重复', value: '重复' }
];

// 发布情况
const publistSituation = [
    {
        label: '已发布',
        value: '已发布'
    },
    {
        label: '未发布',
        value: '未发布'
    },
    {
        label: '部分发布',
        value: '部分发布'
    }
];

// 部署情况
const depolyStituation = [
    {
        label: '已部署',
        value: '已部署'
    },
    {
        label: '未部署',
        value: '未部署'
    },
    {
        label: '部分部署',
        value: '部分部署'
    }
];
// 产品状态
const statusData = [
    {
        label: '进行中',
        value: '进行中'
    },
    {
        label: '已暂停',
        value: '已暂停'
    },
    {
        label: '已关闭',
        value: '已关闭'
    }
];

export default {
    typeData,
    priorityData,
    storyLevelData,
    originalDemandStatus,
    userAndProductDemandStatus,
    demandSourceData,
    demandProgressData,
    deployOrPublishStatusData,
    checkTypeData,
    closeReason,
    publistSituation,
    depolyStituation,
    statusData
};
