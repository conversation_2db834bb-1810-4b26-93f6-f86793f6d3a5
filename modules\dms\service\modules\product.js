/**
 * 需求管理相关服务接口
 */

import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;
    // 根服务对象
    const basePath = basePathInit();
    const service = {
        // 需求管理接口
        product: {
            // 查询产品线
            getProductLine(params) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'productLine/getProductLine',
                    method: 'get',
                    params
                });
            },
            // 查询产品列表
            getProductList(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'product/getProductList',
                    method: 'post',
                    data
                });
            },
            // 查询产品详情
            getProductInfo(params) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'product/getProductInfo',
                    method: 'get',
                    params
                });
            },
            // 查询产品干系人详情
            getStakeholder(params) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'product/getStakeholder',
                    method: 'get',
                    params
                });
            },
            // 新增产品
            addProduct(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'product/insertProduct',
                    method: 'post',
                    data
                });
            },
            // 编辑产品
            editProduct(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'product/updateProduct',
                    method: 'post',
                    data
                });
            },
            // 导入产品
            importProduct(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: `product/importProduct`,
                    method: 'post',
                    data
                });
            },
            // 下载产品导入模板
            toTemplateExcel(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: `common/toTemplateExcel`,
                    method: 'post',
                    responseType: 'blob',
                    data
                });
            },
            // 查询产品审核变更列表
            getProductCheckList(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: `product/getProductCheckList`,
                    method: 'post',
                    data
                });
            },
            // 撤销删除
            deleteChange(params) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: `product/deleteChange`,
                    method: 'get',
                    params
                });
            }
        }
    };

    return service;
};
