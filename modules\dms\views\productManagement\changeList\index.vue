<template>
    <div class="view">
        <project-list
            v-model="activeNavTab"
            :nav-items="navItems"
            :query-params="queryParams"
            :query-config="queryConfig"
            :columns="columns"
            :data="productData"
            :load-data="loadProductData"
            :total="total"
            :page.sync="page"
            :limit.sync="limit"
            @search="handleSearch"
            @reset="handleReset"
            @nav-change="handleNavChange"
            @pagination="handlePagination"
        >
            <!-- 操作列插槽 -->
            <template #actions="{ row }">
                <el-button type="text" size="small" @click="handleEdit(row)">编辑</el-button>
                <el-button type="text" size="small" @click="handleDetails(row)">详情</el-button>
                <el-button type="text" size="small" @click="handleView(row)">撤回</el-button>
                <el-button type="text" size="small" style="color: #f56c6c" @click="handleDelete(row)">删除</el-button>
            </template>
        </project-list>
    </div>
</template>

<script>
import ProjectList from 'dms/views/projectManagement/components/projectList/index.vue';
import { queryConfig, queryParams, navItems, productColumns } from './config.js';

export default {
    name: 'ChangeList',
    components: {
        ProjectList
    },
    data() {
        return {
            // 当前激活的导航标签
            activeNavTab: '待审核',
            // 导航栏配置
            navItems,
            // 查询参数
            queryParams: this.$tools.cloneDeep(queryParams),
            // 查询表单配置
            queryConfig,
            // 表格列配置
            columns: productColumns,
            // 产品数据
            productData: [],
            // 分页配置
            total: 0,
            page: 1,
            limit: 10
        };
    },
    mounted() {
        this.loadProductData();
    },
    methods: {
        // 加载产品数据
        async loadProductData() {
            try {
                const params = {
                    page: this.page,
                    limit: this.limit,
                    ...this.queryParams,
                    type: '变更',
                    status: this.activeNavTab
                };
                // 日期处理
                if (params.applyDate && params.applyDate.length > 0) {
                    params.createStartDate = params.applyDate[0];
                    params.createEndDate = params.applyDate[1];
                }
                if (params.checkDate && params.checkDate.length > 0) {
                    params.closedStartDate = params.checkDate[0];
                    params.closedEndDate = params.checkDate[1];
                }
                const res = await this.$service.dms.product.getProductCheckList(params);
                if (res.code === '0000') {
                    this.productData = res.data || [];
                    this.total = res.total || 0;
                } else {
                    this.$message.error(res.message);
                }
            } catch (error) {
                this.$message.error('系统异常');
            }
        },
        // 处理搜索
        handleSearch() {
            this.page = 1;
            this.loadProductData();
        },
        // 处理重置
        handleReset() {
            this.page = 1;
            this.loadProductData();
        },
        // 处理导航切换
        handleNavChange() {
            this.page = 1;
            this.loadProductData();
        },
        // 处理分页变化
        handlePagination() {
            this.loadProductData();
        },
        // 编辑产品
        handleEdit(row) {
            this.$router.push({
                name: 'AddProduct',
                query: { type: 'edit', row }
            });
        },
        /**
         * 详情
         * @param {Object} row 每行的数据
         */
        handleDetails(row) {
            this.$router.push({ name: 'ChangeDetails', query: { id: row.demandId } });
        },
        handleView(row) {
            this.$confirm('确定要撤销改条数据?', '撤销', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    const params = {
                        checkId: row.checkId,
                        type: '撤销'
                    };
                    this.$service.dms.product.deleteChange(params).then((res) => {
                        if (res.code === '0000') {
                            this.$message.success(res.message);
                            this.loadProductData();
                        } else {
                            this.$message.error(res.message);
                        }
                    });
                })
                .catch(() => {
                    this.$message.info('已取消改操作');
                });
        },
        // 删除产品
        async handleDelete(row) {
            this.$confirm('确定要删除该条数据?', '删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    const params = {
                        checkId: row.checkId,
                        type: '删除'
                    };
                    this.$service.dms.product.deleteChange(params).then((res) => {
                        if (res.code === '0000') {
                            this.$message.success(res.message);
                            this.loadProductData();
                        } else {
                            this.$message.error(res.message);
                        }
                    });
                })
                .catch(() => {
                    this.$message.info('已取消该操作');
                });
        }
    }
};
</script>

<style lang="scss" scoped></style>
