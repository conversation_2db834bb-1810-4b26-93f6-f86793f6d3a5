<template>
    <div class="view">
        <project-list
            :query-params="queryParams"
            :query-config="queryConfig"
            :columns="columns"
            :data="productData"
            :load-data="loadProductData"
            :total="total"
            :page.sync="pageSize"
            :limit.sync="currentPage"
            @search="handleSearch"
            @reset="handleReset"
            @pagination="handlePagination"
        >
            <template #rightNav>
                <el-button type="text" @click="handleAdd()"><i class="el-icon-plus"></i> 新建产品 </el-button>
                <el-button type="text" @click="handleDownload()"
                    ><i class="el-icon-bottom"></i> 下载产品导入模板
                </el-button>
            </template>
            <!-- 操作列插槽 -->
            <template #actions="{ row }">
                <el-button type="text" size="small" @click="handleEdit(row)">编辑</el-button>
                <el-button type="text" size="small" @click="handleDetails(row)">详情</el-button>
            </template>
        </project-list>
    </div>
</template>

<script>
import ProjectList from 'dms/views/projectManagement/components/projectList/index.vue';
import { queryConfig, queryParams, productColumns } from './config.js';

export default {
    name: 'ProductList',
    components: {
        ProjectList
    },
    data() {
        return {
            // 查询参数
            queryParams: this.$tools.cloneDeep(queryParams),
            // 查询表单配置
            queryConfig,
            // 表格列配置
            columns: productColumns,
            // 产品数据
            productData: [{}, {}],
            // 分页配置
            total: 0,
            pageSize: 1,
            currentPage: 10
        };
    },
    mounted() {
        this.loadProductData();
    },
    methods: {
        // 加载产品数据
        async loadProductData() {
            try {
                const params = {
                    page: this.pageSize,
                    limit: this.currentPage,
                    ...this.queryParams
                };
                // 日期处理
                if (params.createDate && params.createDate.length > 0) {
                    params.createStartDate = params.createDate[0];
                    params.createEndDate = params.createDate[1];
                }
                if (params.closedDate && params.closedDate.length > 0) {
                    params.closedStartDate = params.closeDate[0];
                    params.closedEndDate = params.closeDate[1];
                }
                const res = await this.$service.dms.product.getProductList(params);
                if (res.code === '0000') {
                    this.productData = res.data || [];
                    this.total = res.total || 0;
                } else {
                    this.$message.error(res.message || '获取产品列表失败');
                }
            } catch (error) {
                this.$message.error('系统异常');
            }
        },

        // 处理搜索
        handleSearch() {
            this.pageSize = 1;
            this.loadProductData();
        },

        // 处理重置
        handleReset() {
            this.pageSize = 1;
            this.loadProductData();
        },
        // 处理分页变化
        handlePagination() {
            this.loadProductData();
        },
        // 新增产品
        handleAdd() {
            this.$router.push({
                name: 'AddProduct',
                query: { type: 'add', row: {} }
            });
        },

        // 编辑产品
        handleEdit(row) {
            this.$router.push({
                name: 'AddProduct',
                query: { type: 'edit', row }
            });
        },
        // 查看产品详情
        handleDetails(row) {
            this.$router.push({ name: 'DetailsProduct', query: { id: row.id } });
        },
        async handleDownload() {
            const params = {
                excelName: '产品创建模板',
                excelCode: 'createProduct'
            };
            const stream = await this.$service.dms.product.toTemplateExcel({ ...params });
            this.$tools.downloadExprotFile(stream, '模版数据', 'xlsx').catch((e) => {
                this.$tools.message.err(e || '导出失败');
            });
        }
    }
};
</script>

<style lang="scss" scoped></style>
