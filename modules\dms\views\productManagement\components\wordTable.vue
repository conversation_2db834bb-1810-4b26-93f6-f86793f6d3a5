<!-- 标题组件 -->
<template>
    <el-table :data="tableData" border style="width: 100%" height="250px">
        <el-table-column prop="index" label="序号" width="80" align="center" />
        <el-table-column prop="product" label="产品" align="center" />
        <el-table-column prop="subProduct" label="子产品" align="center" />
        <el-table-column prop="module" label="模块" align="center" />
        <el-table-column prop="subModule" label="子模块" align="center" />
        <el-table-column prop="warehouseName" label="仓库名" align="center" />
        <el-table-column prop="operationType" label="操作类型" align="center" />
        <el-table-column prop="operationTime" label="操作时间" align="center" />
        <el-table-column prop="createTime" label="创建时间" align="center" />
        <el-table-column prop="warehouseStatus" label="仓库状态" align="center" />
    </el-table>
</template>

<script>
export default {
    props: {
        tableData: {
            type: Array
        }
    }
};
</script>

<style scoped lang="scss"></style>
