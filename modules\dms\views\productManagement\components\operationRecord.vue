<template>
    <div>
        <div v-for="(item, index) in collapseItems" :key="index" class="collapse-item">
            <div class="collapse-title">
                <div class="approval-note">
                    {{ item.name }}、{{ item.date }} 由{{ item.operator }}
                    <!-- 动态绑定class根据item.status改变颜色 -->
                    <span :class="['approval-status', getStatusClass(item.status)]">{{ item.status }}</span
                    >流程
                </div>
                <div class="approval-comments">审批意见：{{ item.opinion }}</div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        collapseItems: {
            type: Array,
            required: true
        }
    },
    methods: {
        // 根据状态返回对应的class
        getStatusClass(status) {
            if (status === '拒绝') {
                return 'status-rejected';
            } else if (status === '通过') {
                return 'status-approved';
            }
            return '';
        }
    }
};
</script>

<style scoped lang="scss">
.collapse-item {
    width: 100%;
    height: 80px;
    border-bottom: 1px solid #ddd;
    padding: 10px;
}

.collapse-title {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
}

.approval-note,
.approval-comments {
    height: 50%;
    display: flex;
    align-items: center;
    color: #333;
}

.approval-comments {
    font-weight: bolder;
}

/* 定义拒绝和通过的状态样式 */
.status-rejected {
    color: red;
    margin: 0px 5px;
}
.status-approved {
    color: green;
    margin: 0px 5px;
}
</style>
