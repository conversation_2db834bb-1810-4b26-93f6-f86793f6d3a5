<template>
    <div>
        <el-cascader :options="options" v-model="value" @change="handleChange"> </el-cascader>
    </div>
</template>

<script>
export default {
    name: 'DepartmentSelector',
    components: {},
    props: {},
    data() {
        return {};
    },
    computed: {},
    watch: {},
    created() {
        getOptions();
    },
    mounted() {},
    methods: {
        async getOptions() {
            try {
                const res = await this.$service.dms.common.getOrgList({
                    orgCode: '0002'
                });
                if (res.code === '0000') {
                    this.options = this.formatOptions(res.data);
                }
            } catch (error) {
                console.error('获取部门列表失败:', error);
            }
        }
    }
};
</script>

<style lang="scss" scoped></style>
