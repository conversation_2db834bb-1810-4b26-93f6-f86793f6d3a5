<template>
    <div>
        <el-dialog title="临时项目详情" :visible.sync="dialogVisible" width="85%" top="5vh">
            <div class="project-detail">
                <!-- 基本信息 -->
                <div class="title">基本信息</div>
                <el-descriptions :column="2" class="detail-descriptions">
                    <el-descriptions-item label="项目名称">
                        {{ projectData.projectName || '' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="项目经理">
                        {{ projectData.projectManagerName || '' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="开始时间">
                        {{ projectData.startTime || '' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="计划结束时间">
                        {{ projectData.plannedEndTime || '' }}
                    </el-descriptions-item>
                </el-descriptions>

                <!-- 关联信息 -->
                <div class="title">关联信息</div>
                <el-descriptions :column="2" class="detail-descriptions">
                    <el-descriptions-item label="所属部门">
                        {{ projectData.departmentName || '' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="产品线">
                        {{ projectData.productLineName || '' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="产品经理">
                        {{ projectData.productManager || '' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="涉及产品">
                        {{ projectData.involvedProductName || '' }}
                    </el-descriptions-item>
                </el-descriptions>

                <!-- 项目描述 -->
                <div class="title">项目描述</div>
                <el-descriptions :column="1" class="detail-descriptions">
                    <el-descriptions-item label="项目描述">
                        <div class="text-content">{{ projectData.projectDescription || '' }}</div>
                    </el-descriptions-item>
                </el-descriptions>
            </div>

            <div slot="footer" class="dialog-footer">
                <el-button @click="closeDialog">关闭</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: 'TemporaryProjectDetailDialog',
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        projectData: {
            type: Object,
            default: () => ({})
        }
    },

    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        }
    },
    methods: {
        /**
         * 关闭弹窗
         */
        closeDialog() {
            this.dialogVisible = false;
            this.$emit('update:visible', false);
        }
    }
};
</script>

<style lang="scss" scoped>
@import 'dms/views/demandManagement/common/common.scss';

.project-detail {
    .title {
        @include section-title;
        margin-bottom: 16px;
        margin-top: 32px;

        &:first-child {
            margin-top: 0;
        }
    }

    .detail-descriptions {
        margin-bottom: 20px;
    }

    .text-content {
        white-space: pre-wrap;
        word-break: break-word;
        line-height: 1.6;
        min-height: 20px;
        color: #303133;
        max-height: 200px;
        overflow-y: auto;
    }
}

.dialog-footer {
    text-align: right;
}

::v-deep .el-dialog__body {
    padding: 24px;
    max-height: 70vh;
    overflow-y: auto;
}

::v-deep .el-descriptions {
    margin-bottom: 20px;

    .el-descriptions__label {
        font-weight: 600;
        color: #606266;
        background-color: #fafbfc;
        width: 120px;
    }

    .el-descriptions__content {
        color: #303133;
        word-break: break-word;
    }
}

.el-descriptions {
    margin: 0px;
    ::v-deep .el-descriptions-item__label {
        width: 140px;
        font-size: 13px;
        font-weight: 600;
    }
    ::v-deep .el-descriptions-item__content {
        font-size: 13px;
    }
}
</style>
