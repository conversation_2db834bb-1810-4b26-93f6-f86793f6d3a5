<template>
    <div class="view-box">
        <!-- 按钮 -->
        <div class="sprint-btn">
            <el-button @click="cancel">返回</el-button>
        </div>
        <div>
            <!-- 产品结构 -->
            <formula-title :title="basicTitle"></formula-title>
            <!-- 文档代码仓库 -->
            <formula-title :title="woedTitle"></formula-title>
            <word-table :tableData="tableData"></word-table>
            <!-- 操作记录 -->
            <formula-title :title="operationTitle"></formula-title>
            <operation-record :collapseItems="collapseItems"></operation-record>
        </div>
    </div>
</template>

<script>
import formulaTitle from '../components/formulaTitle.vue';
import wordTable from '../components/wordTable.vue';
import operationRecord from '../components/operationRecord.vue';

export default {
    components: { formulaTitle, wordTable, operationRecord },
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            // 标题
            basicTitle: '产品结构',
            woedTitle: '文档&代码仓库',
            operationTitle: '操作记录',
            tableData: [
                {
                    index: 1,
                    product: 'XXXXXXXXXXX',
                    subProduct: '-',
                    module: '-',
                    subModule: '-',
                    warehouseName: 'XXXXXXX',
                    operationType: '移除',
                    operationTime: 'XXXX-XX xxxxxx',
                    createTime: 'XXXX-XX xxxxxx',
                    warehouseStatus: 'XXX'
                },
                {
                    index: 2,
                    product: 'XXXXXXXXXXX',
                    subProduct: '-',
                    module: '-',
                    subModule: '-',
                    warehouseName: 'XXXXXXXXXXX',
                    operationType: '关联',
                    operationTime: 'XXXX-XX xxxxxx',
                    createTime: 'XXXX-XX xxxxxx',
                    warehouseStatus: 'XXX'
                },
                {
                    index: 3,
                    product: '',
                    subProduct: 'XXXXXXX',
                    module: '-',
                    subModule: '-',
                    warehouseName: 'XXXXXXX',
                    operationType: '关联',
                    operationTime: 'XXXX-XX xxxxxx',
                    createTime: 'XXXX-XX xxxxxx',
                    warehouseStatus: 'XXX'
                },
                {
                    index: 4,
                    product: '',
                    subProduct: 'XXXXXXXXXXX',
                    module: '-',
                    subModule: '-',
                    warehouseName: 'XXXXXXX',
                    operationType: '关联',
                    operationTime: 'XXXX-XX xxxxxx',
                    createTime: 'XXXX-XX xxxxxx',
                    warehouseStatus: 'XXX'
                }
            ],
            collapseItems: [
                {
                    name: '1',
                    date: '2025-06-17 17:27:16',
                    operator: '钟振国',
                    status: '创建',
                    opinion: '创建任务初始记录'
                },
                {
                    name: '2',
                    date: '2025-06-17 17:35:00',
                    operator: '钟振国',
                    status: '拒绝',
                    opinion: '创建任务初始记录'
                },
                {
                    name: '3',
                    date: '2025-06-18 16:41:34',
                    operator: '于淼',
                    status: '通过',
                    opinion: '创建任务初始记录'
                },
                {
                    name: '4',
                    date: '2025-06-19 19:06:11',
                    operator: '钟振国',
                    status: '编辑',
                    opinion: '调整任务截止日期和版本'
                }
            ]
        };
    },
    methods: {
        cancel() {
            this.$router.back();
        }
    }
};
</script>

<style lang="scss" scoped></style>
